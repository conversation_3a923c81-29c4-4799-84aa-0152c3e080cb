"use client";

import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  Loader2,
  ChevronDown,
  X,
  <PERSON><PERSON><PERSON><PERSON>gle,
  Refresh<PERSON>w,
  RotateCcw,
} from "lucide-react";
import socketIO from "socket.io-client";
import type { Socket } from "socket.io-client";
import { v4 as uuidv4 } from "uuid";
import "../app/globals.css";

type Message = {
  id: string;
  content: string;
  timestamp: string;
  role: "user" | "assistant" | "system";
  status?: "error" | "sending" | "sent";
};

const AVATAR_URL = "/images/operator1.png";

const ChatWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [inputMessage, setInputMessage] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  // Always set to true - no more terms and conditions
  const hasConsented = true;
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [widgetAnimation, setWidgetAnimation] = useState("");
  const [buttonAnimation, setButtonAnimation] = useState("");
  const [indicatorAnimation, setIndicatorAnimation] = useState("");
  const [isMobile, setIsMobile] = useState(false);

  const socketRef = useRef<typeof Socket | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const messagesRef = useRef<Message[]>([]);
  const hasInitializedRef = useRef(false);
  const pendingMessageRef = useRef<string | null>(null);
  const scrollPositionRef = useRef<number>(0);
  const shouldRestoreScrollRef = useRef<boolean>(false);

  // Check for mobile screen size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check initially
    checkIsMobile();

    // Add resize listener
    window.addEventListener("resize", checkIsMobile);

    // Clean up
    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  // Get or create chatId from localStorage - now with browser check
  const [chatId, setChatId] = useState<string>(() => {
    // Default value in case we're on the server
    const defaultId = uuidv4();

    // Only access localStorage in the browser
    if (typeof window !== "undefined") {
      const savedChatId = localStorage.getItem("chat-widget-id");
      console.log("Retrieved chat ID from localStorage:", savedChatId);

      if (savedChatId) {
        // Validate that it's a proper UUID
        try {
          // Simple UUID validation
          if (
            savedChatId.match(
              /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
            )
          ) {
            console.log("Using existing chat ID:", savedChatId);
            return savedChatId;
          } else {
            console.log("Invalid chat ID format, creating new one");
          }
        } catch (e) {
          console.log("Error validating chat ID, creating new one");
        }
      }

      // Create new chat ID and save it
      console.log("Creating new chat ID:", defaultId);
      localStorage.setItem("chat-widget-id", defaultId);
      return defaultId;
    }

    return defaultId;
  });

  // Consent is now always true by default
  useEffect(() => {
    // Set consent in localStorage for consistency
    if (typeof window !== "undefined") {
      localStorage.setItem("chat-consent", "true");
    }
  }, []);

  // Socket connection handling - now independent of isOpen state
  useEffect(() => {
    console.log("ChatWidget useEffect triggered");
    console.log("socketRef.current:", !!socketRef.current);
    console.log("typeof window:", typeof window);

    // Only create the connection if it doesn't exist yet and we're in the browser
    if (socketRef.current || typeof window === "undefined") {
      console.log(
        "Skipping socket creation - already exists or not in browser"
      );
      return;
    }

    const BACKEND_URL =
      process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3001";
    console.log("Connecting to backend:", BACKEND_URL);
    console.log("Using chat ID:", chatId);

    socketRef.current = socketIO(BACKEND_URL, {
      transports: ["websocket", "polling"],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });
    const socket = socketRef.current;

    socket.on("connect", () => {
      console.log("Socket connected successfully");
      setIsConnected(true);
      if (!hasInitializedRef.current) {
        console.log("Joining chat with ID:", chatId);
        socket.emit("join-chat", { chatId });
        // No need to check terms status anymore
        socket.emit("accept-terms", { chatId }); // Auto-accept terms
        hasInitializedRef.current = true;
      }
    });

    socket.on("disconnect", (reason) => {
      console.log("Socket disconnected:", reason);
      setIsConnected(false);
    });

    socket.on("connect_error", (error) => {
      console.error("Socket connection error:", error);
      setIsConnected(false);
    });

    // We still listen for terms-status for backward compatibility
    socket.on("terms-status", () => {
      // Terms are always accepted now
    });

    socket.on("chat-history", (history: Message[]) => {
      console.log("Received chat history:", history);
      if (messagesRef.current.length === 0) {
        console.log(
          "Setting initial chat history with",
          history.length,
          "messages"
        );
        setMessages(history);
        messagesRef.current = history;

        // Mark that we should restore scroll position after component updates
        if (isOpen) {
          shouldRestoreScrollRef.current = true;
        }
      } else {
        console.log("Ignoring chat history - already have messages");
      }
    });

    socket.on("new-messages", (newMessages: Message[]) => {
      console.log("Received new messages:", newMessages);
      setIsBotTyping(false);
      setMessages((prevMessages) => {
        console.log("Current messages count:", prevMessages.length);
        const existingIds = new Set(prevMessages.map((msg) => msg.id));
        const filteredMessages = newMessages
          .filter(
            (msg) =>
              !existingIds.has(msg.id) &&
              !(
                msg.role === "user" && msg.content === pendingMessageRef.current
              )
          )
          .map((msg) => ({
            ...msg,
            content: msg.content.replace(/<think>.*?<\/think>/g, "").trim(),
          }));

        console.log("Filtered messages to add:", filteredMessages);
        const updatedMessages = [...prevMessages, ...filteredMessages];
        messagesRef.current = updatedMessages;
        console.log("Updated messages count:", updatedMessages.length);

        if (isOpen && isNearBottom()) {
          setTimeout(scrollToBottom, 100);
        }

        return updatedMessages;
      });
      // Clear pending message after processing
      pendingMessageRef.current = "";
    });

    socket.on("error", (error: { message: string; code?: string }) => {
      // Handle API errors
      setIsBotTyping(false);

      // Service error handling simplified - no need to set flag anymore

      // Create a system message to display the error
      const errorMessage: Message = {
        id: uuidv4(),
        content:
          error.code === "SERVICE_UNAVAILABLE"
            ? "Maaf, layanan AI sedang sibuk. Mohon coba lagi setelah beberapa saat."
            : "Terjadi kesalahan dalam memproses pesan Anda. Mohon coba lagi nanti.",
        timestamp: new Date().toISOString(),
        role: "assistant",
        status: "error",
      };

      setMessages((prev) => {
        const updatedMessages = [...prev, errorMessage];
        messagesRef.current = updatedMessages;
        return updatedMessages;
      });

      if (isOpen && isNearBottom()) {
        setTimeout(scrollToBottom, 100);
      }
    });

    socket.on("chat-created", (newChatId: string) => {
      console.log("Received new chat ID from server:", newChatId);
      if (typeof window !== "undefined") {
        localStorage.setItem("chat-widget-id", newChatId);
        console.log("Saved new chat ID to localStorage:", newChatId);
      }
      setChatId(newChatId);
    });

    // Listen for chat reset event
    socket.on(
      "chat-reset",
      (data: { oldChatId: string; newChatId: string }) => {
        console.log("Chat reset received:", data);
        if (typeof window !== "undefined") {
          localStorage.setItem("chat-widget-id", data.newChatId);
          console.log("Updated chat ID in localStorage:", data.newChatId);
        }
        setChatId(data.newChatId);
        setIsResetting(false);
        setMessages([]);
        // Clear any pending message
        pendingMessageRef.current = "";
      }
    );

    // Cleanup function - now only disconnects when component unmounts
    return () => {
      if (chatContainerRef.current) {
        scrollPositionRef.current = chatContainerRef.current.scrollTop;
      }
      socket.disconnect();
      socketRef.current = null;
      hasInitializedRef.current = false;
    };
  }, [chatId]); // No longer dependent on isOpen

  // Handle opening and closing of chat widget
  useEffect(() => {
    if (isOpen && socketRef.current) {
      // When widget opens, fetch latest chat history if needed
      socketRef.current.emit("get-chat-history", { chatId });
      // Mark that we should restore scroll position
      shouldRestoreScrollRef.current = true;

      // For mobile devices, prevent body scrolling when chat is open
      if (isMobile && typeof document !== "undefined") {
        document.body.style.overflow = "hidden";
      }
    } else if (isMobile && typeof document !== "undefined") {
      // Re-enable body scrolling when chat is closed
      document.body.style.overflow = "";
    }

    return () => {
      // Clean up - ensure body scrolling is re-enabled
      if (isMobile && typeof document !== "undefined") {
        document.body.style.overflow = "";
      }
    };
  }, [isOpen, chatId, isMobile]);

  // Effect to restore scroll position when chat reopens
  useEffect(() => {
    if (isOpen && shouldRestoreScrollRef.current && chatContainerRef.current) {
      // Use a short timeout to ensure the DOM has updated
      const timer = setTimeout(() => {
        if (chatContainerRef.current) {
          // If we have a saved position, restore to that position
          if (scrollPositionRef.current > 0) {
            chatContainerRef.current.scrollTop = scrollPositionRef.current;
          }
          shouldRestoreScrollRef.current = false;
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isOpen, messages]);

  // Scroll button visibility
  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container || !hasConsented || !isOpen) return;

    const handleScroll = () => {
      const distanceFromBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight;
      setShowScrollButton(distanceFromBottom > 150);
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [hasConsented, messages, isOpen]);

  // Auto-scroll when bot is typing
  useEffect(() => {
    if (isBotTyping && isOpen && isNearBottom()) {
      scrollToBottom();
    }
  }, [isBotTyping, isOpen]);

  // Save scroll position periodically
  useEffect(() => {
    if (isOpen && chatContainerRef.current) {
      const saveScrollPosition = () => {
        if (chatContainerRef.current) {
          scrollPositionRef.current = chatContainerRef.current.scrollTop;
        }
      };

      // Save position on scroll
      chatContainerRef.current.addEventListener("scroll", saveScrollPosition);

      return () => {
        if (chatContainerRef.current) {
          chatContainerRef.current.removeEventListener(
            "scroll",
            saveScrollPosition
          );
        }
      };
    }
  }, [isOpen]);

  // Helper functions
  const isNearBottom = () => {
    const container = chatContainerRef.current;
    if (!container) return true;
    return (
      container.scrollHeight - container.scrollTop - container.clientHeight <
      100
    );
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    setShowScrollButton(false);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const toggleChat = () => {
    if (isOpen) {
      // Closing the chat - save current scroll position
      if (chatContainerRef.current) {
        scrollPositionRef.current = chatContainerRef.current.scrollTop;
      }

      // Only animate on desktop
      if (!isMobile) {
        // First animate the widget out
        setWidgetAnimation("animate-slide-down");
        setIndicatorAnimation(""); // Reset indicator animation
        setTimeout(() => {
          setIsOpen(false);
          setWidgetAnimation("");
          setButtonAnimation("animate-slide-up");
          setIndicatorAnimation("animate-slide-up"); // Apply same animation as button
        }, 300);
      } else {
        // Immediate close on mobile
        setIsOpen(false);
      }
    } else {
      // Opening the chat
      if (!isMobile) {
        setButtonAnimation("animate-slide-down");
        setIndicatorAnimation("animate-slide-down"); // Apply same animation as button
        setTimeout(() => {
          setIsOpen(true);
          setWidgetAnimation("animate-slide-up");
        }, 300);
      } else {
        // Immediate open on mobile
        setIsOpen(true);
      }
    }
  };

  // Event handlers
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputMessage.trim() || !isConnected || !hasConsented) {
      console.log("Cannot send message:", {
        hasMessage: !!inputMessage.trim(),
        isConnected,
        hasConsented,
      });
      return;
    }

    const messageContent = inputMessage.trim();
    pendingMessageRef.current = messageContent;

    console.log("Sending message:", messageContent, "to chat:", chatId);

    const newMessage: Message = {
      id: uuidv4(),
      content: messageContent,
      timestamp: new Date().toISOString(),
      role: "user",
    };

    setMessages((prev) => {
      const updatedMessages = [...prev, newMessage];
      messagesRef.current = updatedMessages;
      return updatedMessages;
    });

    setInputMessage("");
    setIsBotTyping(true);
    setTimeout(scrollToBottom, 100);

    // Send message to server with better error handling
    if (socketRef.current) {
      socketRef.current.emit("send-message", {
        chatId,
        content: messageContent,
      });
      console.log("Message sent to server");
    } else {
      console.error("Socket not available");
      setIsBotTyping(false);
    }
  };

  // Show reset confirmation dialog
  const showResetConfirmation = () => {
    if (!isConnected || isResetting) return;
    setShowResetConfirm(true);
  };

  // Cancel reset
  const cancelReset = () => {
    setShowResetConfirm(false);
  };

  // Handle chat reset
  const handleResetChat = () => {
    if (!isConnected || isResetting) return;

    setShowResetConfirm(false);
    setIsResetting(true);
    setMessages([]);

    // Generate a new UUID for the new chat
    const newChatId = uuidv4();

    // Emit reset-chat event to the server
    socketRef.current?.emit("reset-chat", {
      chatId,
      newChatId,
    });
  };

  // UI Components
  const TypingAnimation = () => (
    <div className="flex space-x-1 items-center justify-center">
      <div
        className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
        style={{ animationDelay: "0.1s" }}
      ></div>
      <div
        className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
        style={{ animationDelay: "0.2s" }}
      ></div>
      <div
        className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
        style={{ animationDelay: "0.3s" }}
      ></div>
    </div>
  );

  const LoadingState = () => (
    <div className="flex flex-col items-center justify-center h-full space-y-4">
      <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      <p className="text-sm text-gray-600">
        {isResetting
          ? "Mengatur ulang obrolan..."
          : "Menyiapkan asisten obrolan Anda..."}
      </p>
    </div>
  );

  // Reset confirmation dialog
  const ResetConfirmDialog = () => (
    <div className="absolute inset-0 bg-black bg-opacity-50 z-10 flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Reset Percakapan
        </h3>
        <p className="text-sm text-gray-600 mb-6">
          Apakah Anda yakin ingin mengatur ulang percakapan ini? Semua riwayat
          obrolan akan dihapus dan tidak dapat dikembalikan.
        </p>
        <div className="flex justify-end space-x-3">
          <button
            onClick={cancelReset}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            Batal
          </button>
          <button
            onClick={handleResetChat}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Reset
          </button>
        </div>
      </div>
    </div>
  );

  // Determine if connection indicator should be visible
  // Only show when the chat is closed AND user has consented
  const showConnectionIndicator =
    hasConsented && (!isOpen || indicatorAnimation !== "");

  return (
    <>
      {/* Connection status indicator (with animation matching chat button) */}
      {showConnectionIndicator && !isMobile && (
        <div
          className={`fixed bottom-20 right-6 z-50 ${indicatorAnimation}`}
          style={{
            display: isOpen && indicatorAnimation === "" ? "none" : "block",
          }}
        >
          <div
            className={`flex items-center justify-center p-1 rounded-full ${
              isConnected ? "bg-green-500" : "bg-red-500"
            } w-3 h-3`}
          ></div>
        </div>
      )}

      {/* Chat button - Hide on mobile when chat is open */}
      {(!isOpen || (!isMobile && buttonAnimation !== "")) && (
        <button
          onClick={toggleChat}
          className={`fixed bottom-6 right-6 p-4 rounded-full shadow-lg z-50 transition-all bg-blue-500 hover:bg-blue-600 text-white ${
            !isMobile ? buttonAnimation : ""
          }`}
          aria-label="Toggle chat widget"
          style={{
            display:
              isOpen && buttonAnimation === "" && !isMobile ? "none" : "block",
          }}
        >
          <div className="flex items-center justify-center w-6 h-6">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
            </svg>
          </div>
        </button>
      )}

      {/* Chat widget */}
      {(isOpen || (!isMobile && widgetAnimation === "animate-slide-down")) && (
        <div
          className={`fixed bg-white border border-gray-200 flex flex-col overflow-hidden z-[100] ${
            isMobile
              ? "inset-0 m-0 rounded-none"
              : `bottom-0 right-6 w-[500px] h-[600px] rounded-t-lg shadow-xl ${widgetAnimation}`
          }`}
        >
          {/* Header */}
          <div className="p-3 bg-blue-600 text-white border-b flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-400 rounded-full overflow-hidden mr-2">
                <img
                  src={AVATAR_URL}
                  alt="Asisten Virtual"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <h3 className="font-medium text-sm">Asisten Virtual DPMPTSP</h3>
                {hasConsented && (
                  <div className="flex items-center">
                    <div
                      className={`w-2 h-2 rounded-full mr-1 ${
                        isConnected ? "bg-green-400" : "bg-red-400"
                      }`}
                    ></div>
                    <span className="text-xs">
                      {isConnected ? "Online" : "Offline"}
                    </span>
                  </div>
                )}
              </div>
            </div>
            {/* Action buttons */}
            <div className="flex items-center space-x-2">
              {/* Reset button */}
              <button
                onClick={showResetConfirmation}
                className="text-white hover:text-gray-200 transition-colors"
                aria-label="Reset chat"
                disabled={!isConnected || isResetting}
                title="Reset percakapan"
              >
                <RotateCcw
                  size={18}
                  className={isResetting ? "animate-spin" : ""}
                />
              </button>

              {/* Close button */}
              <button
                onClick={toggleChat}
                className="text-white hover:text-gray-200 transition-colors"
                aria-label="Close chat"
              >
                <X size={20} />
              </button>
            </div>
          </div>

          {/* Chat body */}
          <div className="flex-1 pl-4 bg-gray-50 overflow-hidden relative">
            {/* Show reset confirmation dialog if needed */}
            {showResetConfirm && <ResetConfirmDialog />}

            {isInitializing || isResetting ? (
              <LoadingState />
            ) : !isConnected ? (
              <div className="flex flex-col items-center justify-center h-full">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center max-w-xs">
                  <div className="text-red-500 mb-2">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="mx-auto mb-2"
                    >
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </div>
                  <h3 className="font-medium text-red-700 mb-1">
                    Koneksi Teputus
                  </h3>
                  <p className="text-sm text-red-600">
                    Chatbot saat ini sedang offline. Harap segarkan halaman atau
                    periksa koneksi internet Anda.
                  </p>
                </div>
              </div>
            ) : (
              <div
                className="h-full overflow-y-auto pr-4 chat-messages-container"
                ref={chatContainerRef}
              >
                {messages.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-gray-500 text-sm">
                    Obrolan kosong. Ketik sesuatu untuk memulai percakapan!
                  </div>
                ) : (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className={`mb-4 ${
                        message.role === "user" ? "text-right" : "text-left"
                      }`}
                    >
                      {message.role === "assistant" ? (
                        <div className="flex items-start">
                          <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 mr-2 mt-1">
                            <img
                              src={AVATAR_URL}
                              alt="AI Assistant"
                              className="w-full h-full object-cover bg-blue-400"
                            />
                          </div>
                          <div className="max-w-[75%]">
                            <div
                              className={`px-3 py-2 rounded-lg text-sm ${
                                message.status === "error"
                                  ? "bg-red-50 text-red-800 border border-red-200"
                                  : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {message.status === "error" && (
                                <div className="flex items-center mb-1 text-red-500">
                                  <AlertTriangle size={14} className="mr-1" />
                                  <span className="text-xs font-medium">
                                    Error
                                  </span>
                                </div>
                              )}
                              <p className="whitespace-pre-wrap">
                                {message.content}
                              </p>
                              {message.status === "error" && (
                                <div className="mt-2 text-center">
                                  <button
                                    className="inline-flex items-center px-2 py-1 text-xs bg-red-100 hover:bg-red-200 text-red-800 rounded transition-colors"
                                    onClick={() => {
                                      // Find the last user message before this error
                                      const lastUserMsgIndex = [...messages]
                                        .reverse()
                                        .findIndex(
                                          (msg) => msg.role === "user"
                                        );
                                      if (lastUserMsgIndex >= 0) {
                                        const lastUserMsg =
                                          messages[
                                            messages.length -
                                              1 -
                                              lastUserMsgIndex
                                          ];
                                        socketRef.current?.emit(
                                          "send-message",
                                          {
                                            chatId,
                                            content: lastUserMsg.content,
                                            isRetry: true,
                                          }
                                        );
                                        setIsBotTyping(true);
                                      }
                                    }}
                                  >
                                    <RefreshCw size={12} className="mr-1" />{" "}
                                    Coba Lagi
                                  </button>
                                </div>
                              )}
                            </div>
                            <div className="text-xs text-gray-500 ml-1 mt-1">
                              {formatTimestamp(message.timestamp)}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div className="inline-block px-3 py-2 rounded-lg bg-blue-600 text-white text-sm max-w-[75%]">
                            <p className="whitespace-pre-wrap">
                              {message.content}
                            </p>
                          </div>
                          <div className="text-xs text-gray-500 text-right mt-1">
                            {formatTimestamp(message.timestamp)}
                          </div>
                        </div>
                      )}
                    </div>
                  ))
                )}
                {isBotTyping && (
                  <div className="mb-4 text-left">
                    <div className="flex items-start">
                      <div className="w-6 h-6 rounded-full overflow-hidden flex-shrink-0 mr-2 mt-1">
                        <img
                          src={AVATAR_URL}
                          alt="AI Assistant"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <div className="px-3 py-2 rounded-lg bg-gray-100 text-gray-800 text-sm">
                          <TypingAnimation />
                        </div>
                        <div className="text-xs text-gray-500 ml-1 mt-1">
                          {formatTimestamp(new Date().toISOString())}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            )}

            {/* Scroll to bottom button */}
            {hasConsented && showScrollButton && (
              <button
                onClick={scrollToBottom}
                className="absolute bottom-2 left-1/2 transform -translate-x-1/2 rounded-full bg-gray-800 bg-opacity-70 hover:bg-opacity-90 text-white p-2 transition-all shadow-md"
                aria-label="Scroll to bottom"
              >
                <ChevronDown size={20} />
              </button>
            )}
          </div>

          {/* Chat input - Now only displayed when connected and not resetting */}
          {hasConsented && !isInitializing && !isResetting && isConnected && (
            <form
              onSubmit={handleSendMessage}
              className="p-3 border-t border-gray-200 bg-white"
            >
              <div className="flex items-center bg-gray-100 rounded-full px-3 py-1">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Type a reply..."
                  className="flex-1 p-2 bg-transparent border-none focus:outline-none text-sm text-gray-900"
                />
                <button type="submit" className="p-2 text-blue-600">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="m22 2-7 20-4-9-9-4Z" />
                    <path d="M22 2 11 13" />
                  </svg>
                </button>
              </div>
            </form>
          )}
        </div>
      )}
    </>
  );
};

export default ChatWidget;
