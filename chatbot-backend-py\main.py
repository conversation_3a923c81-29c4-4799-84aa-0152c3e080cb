"""
FastAPI Chatbot Backend with Gemini AI, PostgreSQL, and Vector Search
"""
import os
import uuid
import uvicorn
from fastapi import FastAPI, HTTPException, Request, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from pydantic import BaseModel
from typing import Optional, List
import socketio
from dotenv import load_dotenv
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from database import init_db, close_db
from chat_service import ChatService
from pdf_service import PDFService
from check_models import run_all_health_checks

# Load environment variables
load_dotenv()

# Create data directories if they don't exist
os.makedirs("data/docs", exist_ok=True)
os.makedirs("vector_db", exist_ok=True)

# Rate limiting
limiter = Limiter(key_func=get_remote_address)

# Initialize Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins="*",  # Allow all origins for development
    ping_timeout=60,
    ping_interval=25
)

# Pydantic models
class ChatCreateRequest(BaseModel):
    client_provided_id: Optional[str] = None

class MessageRequest(BaseModel):
    chat_id: str
    content: str

class ChatResetRequest(BaseModel):
    chat_id: str
    client_provided_id: Optional[str] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    print("🚀 Starting Chatbot Backend...")

    # Initialize database
    await init_db()
    print("✅ Database initialized")

    # Run health checks
    health_results = await run_all_health_checks()
    if health_results["overall_status"] != "healthy":
        print("⚠️ Some systems are not healthy, but continuing startup...")

    # Initialize PDF service first
    app.state.pdf_service = PDFService()
    print("✅ PDF service initialized")

    # Initialize chat service with PDF service
    app.state.chat_service = ChatService(pdf_service=app.state.pdf_service)
    print("✅ Chat service initialized")

    print("🎉 Chatbot Backend is ready!")

    yield

    # Shutdown
    print("🛑 Shutting down Chatbot Backend...")
    await close_db()
    print("✅ Database connections closed")

# Create FastAPI app
app = FastAPI(
    title="Chatbot Backend",
    description="AI-powered chatbot with PDF processing and vector search",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins="*",  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount Socket.IO
socket_app = socketio.ASGIApp(sio, app)

# Socket.IO event handlers
@sio.event
async def connect(sid, environ):
    """Handle client connection"""
    print(f"Client {sid} connected")
    await sio.emit('connected', {'message': 'Connected to chatbot'}, room=sid)

@sio.event
async def disconnect(sid):
    """Handle client disconnection"""
    print(f"Client {sid} disconnected")

@sio.event
async def join_chat(sid, data):
    """Join chat room and get history"""
    return await handle_join_chat(sid, data)

@sio.on("join-chat")
async def join_chat_hyphen(sid, data):
    """Join chat room and get history - hyphen version"""
    return await handle_join_chat(sid, data)

async def handle_join_chat(sid, data):
    """Join chat room and get history"""
    try:
        chat_id = data.get('chatId') if isinstance(data, dict) else data
        print(f"Client {sid} attempting to join chat: {chat_id}")

        if not chat_id:
            print(f"No chat ID provided by client {sid}")
            await sio.emit('error', {'message': 'Chat ID is required'}, room=sid)
            return

        chat_service = app.state.chat_service

        # Join the socket to this room
        await sio.enter_room(sid, chat_id)
        print(f"Client {sid} entered room {chat_id}")

        # Check if this is a valid chat ID
        if chat_service.is_valid_uuid(chat_id):
            chat_exists_result = await chat_service.chat_exists(chat_id)
            print(f"Chat {chat_id} exists: {chat_exists_result}")

            if not chat_exists_result:
                # This is a valid UUID from localStorage but doesn't exist in DB
                # We'll create it with this ID to preserve the client's history reference
                print(f"Creating chat with client-provided ID: {chat_id}")
                try:
                    await chat_service.create_chat_with_id(chat_id)
                    print(f"Successfully created chat with ID: {chat_id}")
                except Exception as e:
                    print(f"Error creating chat with ID {chat_id}: {e}")
                    # Fallback to creating a new chat
                    new_chat_id = await chat_service.create_chat()
                    print(f"Created fallback chat with ID: {new_chat_id}")
                    await sio.emit('chat-created', new_chat_id, room=sid)
                    await sio.enter_room(sid, new_chat_id)
                    chat_id = new_chat_id
        else:
            # Invalid UUID provided, we'll need to create a new one
            print(f"Invalid UUID {chat_id}, creating new chat")
            new_chat_id = await chat_service.create_chat()
            print(f"Created new chat with ID: {new_chat_id}")
            await sio.emit('chat-created', new_chat_id, room=sid)
            await sio.enter_room(sid, new_chat_id)
            chat_id = new_chat_id

        # Get chat history
        history = await chat_service.get_chat_history_cached(chat_id)
        print(f"Retrieved {len(history)} messages for chat {chat_id}")
        await sio.emit('chat-history', history, room=sid)

        # Send welcome message if this is a new chat (no history)
        if len(history) == 0:
            print(f"Sending welcome message for new chat: {chat_id}")
            welcome_message = await chat_service.send_welcome_message(chat_id)
            await sio.emit('new-messages', [welcome_message], room=sid)

        print(f"Client {sid} successfully joined chat {chat_id}")

    except Exception as e:
        print(f"Error joining chat: {e}")
        import traceback
        traceback.print_exc()
        await sio.emit('error', {'message': 'Failed to join chat'}, room=sid)

@sio.event
async def send_message(sid, data):
    """Send message to chat"""
    return await handle_send_message(sid, data)

@sio.on("send-message")
async def send_message_hyphen(sid, data):
    """Send message to chat - hyphen version"""
    return await handle_send_message(sid, data)

async def handle_send_message(sid, data):
    """Handle sending message to chat"""
    try:
        chat_id = data.get('chatId')
        content = data.get('content')

        print(f"Received message from {sid}: chatId={chat_id}, content_length={len(content) if content else 0}")

        if not chat_id or not content:
            print(f"Missing required data: chatId={chat_id}, content={bool(content)}")
            await sio.emit('error', {'message': 'Chat ID and content are required'}, room=sid)
            return

        chat_service = app.state.chat_service

        # Verify chat exists
        if not await chat_service.chat_exists(chat_id):
            print(f"Chat {chat_id} does not exist")
            await sio.emit('error', {'message': 'Chat not found'}, room=sid)
            return

        # Process user message and get both messages
        print(f"Processing message for chat {chat_id}")
        messages = await chat_service.process_user_message(chat_id, content)

        print(f"Generated {len(messages)} messages for chat {chat_id}")

        # Emit new messages to all clients in the chat room
        await sio.emit('new-messages', messages, room=chat_id)
        print(f"Emitted messages to room {chat_id}")

    except Exception as e:
        print(f"Error sending message: {e}")
        import traceback
        traceback.print_exc()
        await sio.emit('error', {'message': 'Failed to send message'}, room=sid)

@sio.on("accept-terms")
async def accept_terms(sid, data):
    """Handle terms acceptance"""
    try:
        chat_id = data.get('chatId') if isinstance(data, dict) else data
        print(f"Client {sid} accepted terms for chat: {chat_id}")
        # Terms are automatically accepted, just acknowledge
        await sio.emit('terms-status', {'accepted': True}, room=sid)
    except Exception as e:
        print(f"Error handling terms acceptance: {e}")

@sio.event
async def typing_start(sid, data):
    """Handle typing start"""
    chat_id = data.get('chatId') if isinstance(data, dict) else data
    if chat_id:
        # Broadcast to other users in the chat room (exclude sender)
        await sio.emit('user-typing', True, room=chat_id, skip_sid=sid)

@sio.event
async def typing_end(sid, data):
    """Handle typing end"""
    chat_id = data.get('chatId') if isinstance(data, dict) else data
    if chat_id:
        # Broadcast to other users in the chat room (exclude sender)
        await sio.emit('user-typing', False, room=chat_id, skip_sid=sid)

@sio.event
async def reset_chat(sid, data):
    """Reset chat session"""
    try:
        old_chat_id = data.get('chatId')
        # Fix: Frontend sends 'newChatId', not 'clientProvidedId'
        client_provided_id = data.get('newChatId') or data.get('clientProvidedId')

        if not old_chat_id:
            await sio.emit('error', {'message': 'Chat ID is required'}, room=sid)
            return

        chat_service = app.state.chat_service

        print(f"Resetting chat {old_chat_id} with client provided ID: {client_provided_id or 'none'}")

        # Leave the current chat room
        await sio.leave_room(sid, old_chat_id)

        # Create a new chat with the client provided ID or generate a new one
        new_chat_id = None

        if client_provided_id and chat_service.is_valid_uuid(client_provided_id):
            # Check if the client provided ID already exists
            chat_exists_result = await chat_service.chat_exists(client_provided_id)

            if chat_exists_result:
                # If it exists, we'll use a new random ID instead
                new_chat_id = await chat_service.create_chat()
                print(f"Client provided ID {client_provided_id} already exists, using new ID: {new_chat_id}")
            else:
                # Use the client provided ID to create a new chat
                try:
                    new_chat_id = await chat_service.create_chat_with_id(client_provided_id)
                    print(f"Created new chat with client provided ID: {new_chat_id}")
                except Exception as e:
                    print(f"Error creating chat with ID {client_provided_id}: {e}")
                    # Fallback to creating a new chat with a random ID
                    new_chat_id = await chat_service.create_chat()
        else:
            # Generate a new chat ID
            new_chat_id = await chat_service.create_chat()
            print(f"Created new chat with generated ID: {new_chat_id}")

        # Join the new chat room
        await sio.enter_room(sid, new_chat_id)

        # Send the new chat ID to the client
        await sio.emit('chat-reset', {'oldChatId': old_chat_id, 'newChatId': new_chat_id}, room=sid)

        # Send welcome message for the new chat
        welcome_message = await chat_service.send_welcome_message(new_chat_id)
        await sio.emit('new-messages', [welcome_message], room=sid)

        print(f"Chat reset complete: {old_chat_id} -> {new_chat_id}")

    except Exception as e:
        print(f"Error resetting chat: {e}")
        await sio.emit('error', {'message': 'Failed to reset chat'}, room=sid)

# REST API Routes
@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Chatbot Backend is running!", "status": "healthy"}

@app.get("/health")
@limiter.limit("100/15minutes")
async def health_check(request: Request):
    """Detailed health check"""
    try:
        health_results = await run_all_health_checks()
        return health_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@app.post("/api/chat")
@limiter.limit("100/15minutes")
async def create_chat_endpoint(request: Request):
    """Create new chat session"""
    try:
        chat_service = app.state.chat_service
        chat_id = await chat_service.create_chat()

        return {"chatId": chat_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create chat: {str(e)}")

@app.get("/api/chat/{chat_id}/history")
@limiter.limit("100/15minutes")
async def get_chat_history_endpoint(request: Request, chat_id: str):
    """Get chat history"""
    try:
        chat_service = app.state.chat_service

        if not chat_service.is_valid_uuid(chat_id):
            raise HTTPException(status_code=400, detail="Invalid chat ID format")

        history = await chat_service.get_chat_history_cached(chat_id)
        return history  # Return array directly like TypeScript version

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get chat history: {str(e)}")

@app.post("/api/pdf/upload")
@limiter.limit("100/15minutes")
async def upload_pdf(request: Request, file: UploadFile = File(...)):
    """Upload PDF document"""
    try:
        if not file.filename:
            raise HTTPException(status_code=400, detail="No PDF file uploaded")

        pdf_service = app.state.pdf_service
        result = await pdf_service.upload_pdf(file)

        # Return format matching TypeScript version
        return {
            "success": True,
            "documentId": result.get("document_id"),
            "name": file.filename,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload PDF: {str(e)}")

@app.get("/api/pdf/list")
@limiter.limit("100/15minutes")
async def list_pdfs(request: Request):
    """List uploaded PDF documents"""
    try:
        pdf_service = app.state.pdf_service
        documents = pdf_service.list_documents()

        # Return only the necessary information like TypeScript version
        document_list = []
        for doc in documents:
            document_list.append({
                "id": doc.get("id"),
                "name": doc.get("original_filename", doc.get("filename")),
                "pageCount": doc.get("page_count", 0),
                "createdAt": doc.get("created_at"),
                "fileSize": doc.get("file_size", 0),
            })

        return document_list
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list PDFs: {str(e)}")

@app.delete("/api/pdf/{document_id}")
@limiter.limit("100/15minutes")
async def delete_pdf(request: Request, document_id: str):
    """Delete PDF document"""
    try:
        pdf_service = app.state.pdf_service
        success = await pdf_service.delete_document(document_id)

        if success:
            return {"success": True}
        else:
            raise HTTPException(status_code=404, detail="Document not found")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete PDF: {str(e)}")

@app.post("/api/search")
@limiter.limit("100/15minutes")
async def search_documents(request: Request, query: dict):
    """Search documents"""
    try:
        search_query = query.get("query", "")
        if not search_query:
            raise HTTPException(status_code=400, detail="Query is required")

        pdf_service = app.state.pdf_service
        results = await pdf_service.search_documents(search_query, top_k=5)
        return {"results": results}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to search documents: {str(e)}")

# Add rate limiting error handler
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

if __name__ == "__main__":
    port = int(os.getenv("PORT", 3001))
    uvicorn.run(
        "main:socket_app",
        host="0.0.0.0",
        port=port,
        reload=True if os.getenv("NODE_ENV") == "development" else False
    )
